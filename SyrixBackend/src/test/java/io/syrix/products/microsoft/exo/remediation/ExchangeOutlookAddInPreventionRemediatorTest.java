package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.domain.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExchangeOutlookAddInPreventionRemediator (MS.EXO.21.1v1).
 * Tests add-in installation prevention by removing restricted roles from role assignment policies.
 */
@ExtendWith(MockitoExtension.class)
class ExchangeOutlookAddInPreventionRemediatorTest {

    @Mock
    private MicrosoftGraphClient graphClient;
    
    @Mock
    private PowerShellClient exchangeClient;
    
    @Mock
    private ExchangeRemediationContext remediationContext;
    
    @Mock
    private ExchangeRemediationConfig remediationConfig;

    @Captor
    private ArgumentCaptor<PowerShellClient.CommandRequest> requestCaptor;

    private ExchangeOutlookAddInPreventionRemediator remediator;
    private ObjectMapper objectMapper;
    private ObjectNode configNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        configNode = objectMapper.createObjectNode();
        remediator = new ExchangeOutlookAddInPreventionRemediator(
            graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
    }

    @Nested
    @DisplayName("Policy Compliance Tests")
    class PolicyComplianceTests {

        @Test
        @DisplayName("Should detect compliant policies with no restricted roles")
        void shouldDetectCompliantPolicies() {
            // Given
            ArrayNode policies = createCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.REQUIREMENT_MET);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.21.1v1");
            
            // Should not attempt any PowerShell commands for compliant policies
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }

        @Test
        @DisplayName("Should detect non-compliant policies with restricted roles")
        void shouldDetectNonCompliantPolicies() {
            // Given
            ArrayNode policies = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result.getPolicyId()).isEqualTo("MS.EXO.21.1v1");
            assertThat(result.getMessage()).contains("Removed restricted roles");
            assertThat(result.getMessage()).contains("My Custom Apps");
            assertThat(result.getMessage()).contains("My Marketplace Apps");
            assertThat(result.getMessage()).contains("My ReadWriteMailbox Apps");
            
            // Should execute Get-ManagementRoleAssignment and Remove-ManagementRoleAssignment commands
            // For 2 policies with 3 roles each = 6 Get + 6 Remove = 12 total commands minimum
            verify(exchangeClient, atLeast(6)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify PowerShell commands include Get-ManagementRoleAssignment and Remove-ManagementRoleAssignment
            List<String> commandNames = requestCaptor.getAllValues().stream()
                .map(PowerShellClient.CommandRequest::getCmdletName)
                .distinct()
                .toList();
            
            assertThat(commandNames).contains(ExoConstants.GET_MANAGEMENT_ROLE_ASSIGNMENT);
            assertThat(commandNames).contains(ExoConstants.REMOVE_MANAGEMENT_ROLE_ASSIGNMENT);
        }

        @Test
        @DisplayName("Should handle mixed compliant and non-compliant policies")
        void shouldHandleMixedPolicies() {
            // Given
            ArrayNode policies = createMixedPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            
            // Should only execute commands for non-compliant policies (1 policy with 1 restricted role = 2 commands minimum)
            verify(exchangeClient, atLeast(2)).executeCmdletCommand(any());
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle missing configuration data")
        void shouldHandleMissingConfigurationData() {
            // Given - no configuration data
            configNode.removeAll();

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getMessage()).contains(ExoConstants.ERROR_NO_ROLE_ASSIGNMENT_POLICIES);
        }

        @Test
        @DisplayName("Should handle PowerShell command failures")
        void shouldHandlePowerShellFailures() {
            // Given
            ArrayNode policies = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);
            
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell error")));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getMessage()).contains("Failed to update Role Assignment Policy");
        }

        @Test
        @DisplayName("Should handle partial failures")
        void shouldHandlePartialFailures() {
            // Given
            ArrayNode policies = createNonCompliantPolicies();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);
            
            // Mock one success and one failure
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Second policy failed")));

            // When
            PolicyChangeResult result = remediator.remediate_().join();

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.PARTIAL_SUCCESS);
            assertThat(result.getMessage()).contains("Fixed 1");
            assertThat(result.getMessage()).contains("failed to fix 1");
        }
    }

    @Nested
    @DisplayName("Rollback Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should successfully rollback policy changes by adding roles back")
        void shouldSuccessfullyRollbackChanges() {
            // Given
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(rollbackResult.getMessage()).contains("Successfully restored");
            
            // Should execute New-ManagementRoleAssignment commands for each role that was removed
            // 2 roles for first policy + 1 role for second policy = 3 commands
            verify(exchangeClient, times(3)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify rollback uses New-ManagementRoleAssignment (NOT Remove-ManagementRoleAssignment)
            List<String> commandNames = requestCaptor.getAllValues().stream()
                .map(PowerShellClient.CommandRequest::getCmdletName)
                .distinct()
                .toList();
            
            assertThat(commandNames).containsExactly("New-ManagementRoleAssignment");
            assertThat(commandNames).doesNotContain(ExoConstants.REMOVE_MANAGEMENT_ROLE_ASSIGNMENT);
            
            // Verify the specific roles being restored
            List<PowerShellClient.CommandRequest> allRequests = requestCaptor.getAllValues();
            List<String> rolesBeingRestored = allRequests.stream()
                .map(req -> (String) req.getParameters().get(ExoConstants.ROLE))
                .toList();
            
            assertThat(rolesBeingRestored).containsExactlyInAnyOrder(
                "My Custom Apps", "My Marketplace Apps", "My ReadWriteMailbox Apps"
            );
            
            // Verify the correct policies are being targeted
            List<String> policiesBeingModified = allRequests.stream()
                .map(req -> (String) req.getParameters().get(ExoConstants.POLICY))
                .toList();
            
            assertThat(policiesBeingModified).contains(
                "Default Role Assignment Policy", "Executive Role Assignment Policy"
            );
        }

        @Test
        @DisplayName("Should handle rollback when no changes to rollback")
        void shouldHandleNoChangesToRollback() {
            // Given
            PolicyChangeResult emptyResult = new PolicyChangeResult();

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(emptyResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getMessage()).contains("No changes found in fix result");
            
            // Should not execute any PowerShell commands
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }
        
        @Test
        @DisplayName("Should handle rollback of failed remediation changes")
        void shouldHandleFailedRemediationChanges() {
            // Given
            PolicyChangeResult failedResult = new PolicyChangeResult();
            failedResult.setPolicyId("MS.EXO.21.1v1");
            failedResult.setResult(RemediationResult.FAILED);
            
            List<ParameterChangeResult> changes = new ArrayList<>();
            ParameterChangeResult failedChange = new ParameterChangeResult()
                .timeStamp(Instant.now())
                .parameter("Identity:Test Policy")
                .status(ParameterChangeStatus.FAILED);
            changes.add(failedChange);
            failedResult.setChanges(changes);

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(failedResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(rollbackResult.getMessage()).contains("skipped");
            
            // Should not execute any PowerShell commands for failed changes
            verify(exchangeClient, never()).executeCmdletCommand(any());
        }
        
        @Test
        @DisplayName("Should handle partial rollback failures")
        void shouldHandlePartialRollbackFailures() {
            // Given
            PolicyChangeResult originalResult = createSuccessfulRemediationResult();
            
            // Mock first call to succeed, second to fail
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell error")))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            PolicyChangeResult rollbackResult = remediator.rollback(originalResult).join();

            // Then
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.PARTIAL_SUCCESS);
            assertThat(rollbackResult.getMessage()).contains("Fixed 1 policies, failed to fix 1 policies");
            
            // Should still attempt all rollback operations
            verify(exchangeClient, times(3)).executeCmdletCommand(any());
        }
    }

    @Nested
    @DisplayName("Role Filtering Tests")
    class RoleFilteringTests {

        @Test
        @DisplayName("Should correctly identify and remove restricted roles")
        void shouldFilterRestrictedRoles() {
            // Given
            ArrayNode policies = createPolicyWithAllRoles();
            configNode.set(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, policies);
            
            // Mock Get-ManagementRoleAssignment to return role assignments
            ArrayNode roleAssignments = objectMapper.createArrayNode();
            ObjectNode assignment = objectMapper.createObjectNode();
            assignment.put("Identity", "test-assignment-id");
            roleAssignments.add(assignment);
            
            JsonNode successResponse = objectMapper.createObjectNode();
            when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(roleAssignments))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

            // When
            remediator.remediate_().join();

            // Then
            verify(exchangeClient, atLeast(6)).executeCmdletCommand(requestCaptor.capture());
            
            // Verify Get-ManagementRoleAssignment calls are made for restricted roles
            List<PowerShellClient.CommandRequest> getRequests = requestCaptor.getAllValues().stream()
                .filter(req -> ExoConstants.GET_MANAGEMENT_ROLE_ASSIGNMENT.equals(req.getCmdletName()))
                .toList();
            
            assertThat(getRequests).hasSize(3); // Should query for 3 restricted roles
            
            // Verify the queries target the restricted roles
            List<String> queriedRoles = getRequests.stream()
                .map(req -> (String) req.getParameters().get(ExoConstants.ROLE))
                .toList();
            
            assertThat(queriedRoles).containsExactlyInAnyOrder(
                "My Custom Apps", "My Marketplace Apps", "My ReadWriteMailbox Apps"
            );
        }
    }

    // Helper methods for creating test data
    private ArrayNode createCompliantPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("Identity", "Default Role Assignment Policy");
        ArrayNode roles1 = objectMapper.createArrayNode();
        roles1.add("MyBaseOptions");
        roles1.add("MyContactInformation");
        policy1.set("AssignedRoles", roles1);
        policies.add(policy1);
        
        return policies;
    }

    private ArrayNode createNonCompliantPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy1 = objectMapper.createObjectNode();
        policy1.put("Identity", "Default Role Assignment Policy");
        ArrayNode roles1 = objectMapper.createArrayNode();
        roles1.add("MyBaseOptions");
        roles1.add("My Custom Apps");
        roles1.add("My Marketplace Apps");
        policy1.set("AssignedRoles", roles1);
        policies.add(policy1);
        
        ObjectNode policy2 = objectMapper.createObjectNode();
        policy2.put("Identity", "Executive Role Assignment Policy");
        ArrayNode roles2 = objectMapper.createArrayNode();
        roles2.add("MyContactInformation");
        roles2.add("My ReadWriteMailbox Apps");
        policy2.set("AssignedRoles", roles2);
        policies.add(policy2);
        
        return policies;
    }

    private ArrayNode createMixedPolicies() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        // Compliant policy
        ObjectNode compliantPolicy = objectMapper.createObjectNode();
        compliantPolicy.put("Identity", "Restricted Role Assignment Policy");
        ArrayNode compliantRoles = objectMapper.createArrayNode();
        compliantRoles.add("MyBaseOptions");
        compliantRoles.add("MyContactInformation");
        compliantPolicy.set("AssignedRoles", compliantRoles);
        policies.add(compliantPolicy);
        
        // Non-compliant policy
        ObjectNode nonCompliantPolicy = objectMapper.createObjectNode();
        nonCompliantPolicy.put("Identity", "Default Role Assignment Policy");
        ArrayNode nonCompliantRoles = objectMapper.createArrayNode();
        nonCompliantRoles.add("MyBaseOptions");
        nonCompliantRoles.add("My Custom Apps");
        nonCompliantPolicy.set("AssignedRoles", nonCompliantRoles);
        policies.add(nonCompliantPolicy);
        
        // Another compliant policy
        ObjectNode anotherCompliantPolicy = objectMapper.createObjectNode();
        anotherCompliantPolicy.put("Identity", "Sales Role Assignment Policy");
        ArrayNode anotherCompliantRoles = objectMapper.createArrayNode();
        anotherCompliantRoles.add("MyDistributionGroups");
        anotherCompliantPolicy.set("AssignedRoles", anotherCompliantRoles);
        policies.add(anotherCompliantPolicy);
        
        return policies;
    }

    private ArrayNode createPolicyWithAllRoles() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        ObjectNode policy = objectMapper.createObjectNode();
        policy.put("Identity", "Full Role Assignment Policy");
        ArrayNode roles = objectMapper.createArrayNode();
        
        // Add safe roles
        roles.add("MyBaseOptions");
        roles.add("MyContactInformation");
        roles.add("MyDistributionGroups");
        
        // Add restricted roles that should be removed
        roles.add("My Custom Apps");
        roles.add("My Marketplace Apps");
        roles.add("My ReadWriteMailbox Apps");
        
        policy.set("AssignedRoles", roles);
        policies.add(policy);
        
        return policies;
    }

    private PolicyChangeResult createSuccessfulRemediationResult() {
        // Create a realistic remediation result with ParameterChangeResult entries
        // that the rollback method can use to restore the original state
        
        PolicyChangeResult result = new PolicyChangeResult();
        result.setPolicyId("MS.EXO.21.1v1");
        result.setResult(RemediationResult.SUCCESS);
        result.setMessage("Test remediation completed");
        
        // Create realistic parameter changes that represent what the remediation would produce
        List<ParameterChangeResult> changes = new ArrayList<>();
        
        // Simulate a policy where "My Custom Apps" and "My Marketplace Apps" were removed
        ParameterChangeResult change1 = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter("Identity:Default Role Assignment Policy,removed_roles:My Custom Apps,My Marketplace Apps")
            .prevValue("MyBaseOptions,My Custom Apps,My Marketplace Apps")
            .newValue("MyBaseOptions")
            .status(ParameterChangeStatus.SUCCESS);
        changes.add(change1);
        
        // Simulate another policy where "My ReadWriteMailbox Apps" was removed
        ParameterChangeResult change2 = new ParameterChangeResult()
            .timeStamp(Instant.now())
            .parameter("Identity:Executive Role Assignment Policy,removed_roles:My ReadWriteMailbox Apps")
            .prevValue("MyContactInformation,My ReadWriteMailbox Apps")
            .newValue("MyContactInformation")
            .status(ParameterChangeStatus.SUCCESS);
        changes.add(change2);
        
        result.setChanges(changes);
        return result;
    }
}