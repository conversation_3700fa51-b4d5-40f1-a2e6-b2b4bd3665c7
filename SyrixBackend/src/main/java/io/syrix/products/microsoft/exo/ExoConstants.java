package io.syrix.products.microsoft.exo;

/**
 * Constants for Exchange Online Configuration Service
 */
public class ExoConstants {
    // PowerShell Commands
    public static final String GET_DKIM_SIGNING_CONFIG = "Get-DkimSigningConfig";
    public static final String GET_HOSTED_CONTENT_FILTER_POLICY = "Get-HostedContentFilterPolicy";
    public static final String GET_REMOTE_DOMAIN = "Get-RemoteDomain";
    public static final String GET_ORGANIZATION_CONFIG = "Get-OrganizationConfig";
    public static final String GET_TRANSPORT_CONFIG = "Get-TransportConfig";
    public static final String GET_HOSTED_CONNECTION_FILTER_POLICY = "Get-HostedConnectionFilterPolicy";
    public static final String GET_ANTI_PHISH_POLICY = "Get-AntiPhishPolicy";
    public static final String GET_ANTI_PHISH_RULE = "Get-AntiPhishRule";
    public static final String SET_TRANSPORT_CONFIG = "Set-TransportConfig";
    public static final String SET_ORGANIZATION_CONFIG = "Set-OrganizationConfig";
    public static final String SET_REMOTE_DOMAIN = "Set-RemoteDomain";
    public static final String GET_PROTECTION_ALERT = "Get-ProtectionAlert";
    public static final String GET_ROLE_ASSIGNMENT_POLICY = "Get-RoleAssignmentPolicy";
    public static final String SET_ROLE_ASSIGNMENT_POLICY = "Set-RoleAssignmentPolicy";
    public static final String GET_MANAGEMENT_ROLE = "Get-ManagementRole";
    public static final String GET_MANAGEMENT_ROLE_ASSIGNMENT = "Get-ManagementRoleAssignment";
    public static final String REMOVE_MANAGEMENT_ROLE_ASSIGNMENT = "Remove-ManagementRoleAssignment";
    
    // Configuration Parameters
    public static final String SMTP_CLIENT_AUTH_DISABLED = "SmtpClientAuthenticationDisabled";
    public static final String AUDIT_DISABLED = "AuditDisabled";
    public static final String AUTO_FORWARD_ENABLED = "AutoForwardEnabled";
    public static final String PARAM_DOMAINS = "Domains";
    public static final String ERROR_ACTION = "ErrorAction";
    public static final String IDENTITY = "Identity";
    public static final String ASSIGNED_ROLES = "AssignedRoles";
    public static final String ROLES = "Roles";
    public static final String ROLE = "Role";
    public static final String POLICY = "Policy";
    public static final String ROLE_ASSIGNEE = "RoleAssignee";
    public static final String ROLE_TYPE = "RoleType";
    public static final String NAME = "Name";
    
    // Status Values and Error Fields
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAILED = "failed";
    public static final String UNKNOWN_ERROR = "Unknown error";
    public static final String ERROR_FIELD = "Error";
    public static final String ERROR_ACTION_STOP = "Stop";
    
    // DKIM related constants
    public static final String SELECTOR1 = "selector1";
    public static final String SELECTOR2 = "selector2";
    public static final String ENABLED = "Enabled";
    
    // Config keys
    public static final String CONFIG_KEY_ORGANIZATION = "organization_config";
    public static final String CONFIG_KEY_CONTENT_FILTER_POLICY = "content_filter_policy";
    public static final String CONFIG_KEY_TRANSPORT = "transport_config";
    public static final String CONFIG_KEY_REMOTE_DOMAINS = "remote_domains";
    public static final String CONFIG_KEY_DKIM_CONFIG = "dkim_config";
    public static final String CONFIG_KEY_SHARING_POLICY = "sharing_policy";
    public static final String CONFIG_KEY_TRANSPORT_RULE = "transport_rule";
    public static final String CONFIG_KEY_CONN_FILTER = "conn_filter";
    public static final String CONFIG_KEY_SHARED_MAILBOX = "shared_mailboxes_with_signin";
    public static final String CONFIG_KEY_MALWARE_POLICY = "malware_policy";
    public static final String CONFIG_KEY_MALWARE_RULE = "malware_rule";
    public static final String CONFIG_KEY_ANTI_PHISH_POLICY = "anti_phish_policy";
    public static final String CONFIG_KEY_ANTI_PHISH_RULE = "anti_phish_rule";
    public static final String CONFIG_KEY_SAFE_LINKS_POLICY = "safe_links_policy";
    public static final String CONFIG_KEY_SAFE_LINKS_RULE = "safe_links_rule";
    public static final String CONFIG_KEY_ALERTS = "alerts";
    public static final String CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT = "role_assignment_addin_audit";
    // SPF related constants
    public static final String DEFAULT_SPF_RECORD = "v=spf1 include:spf.protection.outlook.com -all";
    public static final String DOMAIN_NAME = "DomainName";
    public static final String FIELD_NAME = "Name";
    
    // Error messages
    public static final String ERROR_NO_TRANSPORT_RULE = "No transport rules found";
    public static final String ERROR_NO_DOMAINS = "No domains found or invalid response";
    public static final String ERROR_NO_VERIFIED_DOMAINS = "No verified domains found";
    public static final String ERROR_NO_SHARING_POLICIES_FOUND = "No sharing policies found";
    public static final String ERROR_NO_CONNECTION_FILTER_POLICIES_FOUND = "No connection filter policies found";
    public static final String ERROR_NO_ORGANIZATION_CONFIG = "No organization config found";
    public static final String ERROR_NO_SHARED_MAILBOX = "No Shared mailboxes with signin found";
    public static final String ERROR_NO_MALWARE_POLICY = "No malware policy found";
    public static final String ERROR_NO_MALWARE_RULE = "No malware rule found";
//    public static final String ERROR_NO_ANTI_PHISH_POLICY = "No anti-phishing policy found";
//    public static final String ERROR_NO_ANTI_PHISH_RULE = "No anti-phishing rule found";
    public static final String ERROR_NO_CONTENT_FILTER_POLICY = "No content filter policies found in configuration";
    public static final String ERROR_NO_ROLE_ASSIGNMENT_POLICIES = "No role assignment policies found for add-in compliance audit";
    
    // Success messages
    public static final String SUCCESS_ALL_DOMAINS_COMPLIANT = "All domains have compliant SPF records with hard fail (-all)";
    public static final String SUCCESS_MAILBOX_AUDITING_ALREADY_ENABLED = "Mailbox auditing is already enabled for the organization";
    public static final String SUCCESS_MAILBOX_AUDITING_ENABLED = "Mailbox auditing enabled successfully for the organization";
    public static final String SUCCESS_SMTP_AUTH_DISABLED = "SMTP authentication disabled for the organization";

    // Customer Lockbox related constants
    public static final String CUSTOMER_LOCKBOX_ENABLED = "CustomerLockboxEnabled";
    public static final String SUCCESS_CUSTOMER_LOCKBOX_ALREADY_ENABLED = "Customer Lockbox is already enabled for the organization";

//    public static final String SUCCESS_CUSTOMER_LOCKBOX_ENABLED = "Customer Lockbox enabled successfully for the organization";
}