package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import io.syrix.domain.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Remediates MS.EXO.21.1v1 policy by preventing users from installing Outlook add-ins.
 * This implementation removes restricted roles ("My Custom Apps", "My Marketplace Apps", 
 * "My ReadWriteMailbox Apps") from all role assignment policies to prevent end-user 
 * add-in installation as required by CIS Microsoft 365 Foundations Benchmark v5.0.0 
 * control 6.3.1.
 */
@PolicyRemediator("MS.EXO.21.1v1")
public class ExchangeOutlookAddInPreventionRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {

    // Fallback restricted roles if discovery fails (language-independent approach uses discovered roles from configNode)

    public ExchangeOutlookAddInPreventionRemediator(MicrosoftGraphClient graphClient, 
                                                   PowerShellClient exchangeClient, 
                                                   ObjectNode configNode, 
                                                   ExchangeRemediationContext exchangeRemediationContext, 
                                                   ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(jsonMapper::valueToTree);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        logger.info("Starting remediation for {} (Outlook Add-In Prevention Policy)", getPolicyId());

        List<RoleAssignmentPolicy> policies = getRoleAssignmentPolicies();
        if (policies.isEmpty()) {
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ExoConstants.ERROR_NO_ROLE_ASSIGNMENT_POLICIES));
        }

        logger.debug("Found {} role assignment policies to evaluate", policies.size());
        
        List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
                .filter(this::hasRestrictedRoles)
                .map(policy -> updateRoleAssignmentPolicy(policy, removeRestrictedRoles(policy.assignedRoles), policy.assignedRoles))
                .toList();

        logger.debug("Found {} policies that need remediation", results.size());
        
        if (results.isEmpty()) {
            logger.debug("No policies found with restricted roles - requirement already met");
            return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
        }

        return combineResults(results);
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        logger.info("Starting rollback for {} (Outlook Add-In Prevention Policy)", getPolicyId());
        try {
            List<ParameterChangeResult> changes = fixResult.getChanges();
            if (changes == null || changes.isEmpty()) {
                return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
            }

            List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

            for (ParameterChangeResult change : changes) {
                if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
                    logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), change.getParameter());
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
                            "Rollback the policy " + getPolicyId() + " identity: " + change.getParameter() + " skipped", List.of(change))));
                    continue;
                }

                // Extract policy identity and roles using robust parameter parsing
                Map<String, String> paramData = ParameterEncoder.decode(change.getParameter());
                String identity = paramData.get("identity");
                
                if (identity == null || identity.trim().isEmpty()) {
                    logger.error("Invalid parameter format - missing identity: {}", change.getParameter());
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
                            "Invalid parameter format - missing identity: " + change.getParameter(), List.of(change))));
                    continue;
                }
                
                String prevRolesStr = (String) change.getPrevValue();
                String newRolesStr = (String) change.getNewValue();
                List<String> prevRoles = prevRolesStr != null && !prevRolesStr.trim().isEmpty() 
                    ? List.of(prevRolesStr.split(",")) 
                    : new ArrayList<>();
                List<String> newRoles = newRolesStr != null && !newRolesStr.trim().isEmpty() 
                    ? List.of(newRolesStr.split(",")) 
                    : new ArrayList<>();

                // Find roles that were removed during remediation (in prevRoles but not in newRoles)
                List<String> rolesToRestore = prevRoles.stream()
                        .filter(role -> !newRoles.contains(role))
                        .toList();

                if (rolesToRestore.isEmpty()) {
                    logger.info("No roles to restore for policy: {}", identity);
                    ParameterChangeResult rollbackChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(change.getParameter())
                            .prevValue(newRolesStr) // Current state
                            .newValue(prevRolesStr) // Target state (same as current)
                            .status(ParameterChangeStatus.SUCCESS);
                    results.add(CompletableFuture.completedFuture(IPolicyRemediator.success_(getPolicyId(), 
                            "No roles to restore for policy: " + identity, List.of(rollbackChange))));
                } else {
                    results.add(restoreRolesToPolicy(identity, rolesToRestore, newRoles, prevRoles, change.getParameter()));
                }
            }

            return combineResults(results);
        } catch (Exception ex) {
            logger.error("Rollback the policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
        }
    }

    private CompletableFuture<PolicyChangeResult> updateRoleAssignmentPolicy(RoleAssignmentPolicy policy, 
                                                                           List<String> newRoles, 
                                                                           List<String> prevRoles) {
        logger.trace("Preparing to remove restricted roles from policy: {}", policy.identity);

        // Find roles that need to be removed (roles in prevRoles but not in newRoles)
        List<String> rolesToRemove = prevRoles.stream()
                .filter(role -> !newRoles.contains(role))
                .toList();

        if (rolesToRemove.isEmpty()) {
            logger.info("No restricted roles found to remove from policy: {}", policy.identity);
            ParameterChangeResult paramChange = new ParameterChangeResult()
                    .timeStamp(Instant.now())
                    .parameter(ParameterEncoder.encode(policy.identity, Collections.emptyList()))
                    .prevValue(String.join(",", prevRoles))
                    .newValue(String.join(",", newRoles))
                    .status(ParameterChangeStatus.SUCCESS);
            return CompletableFuture.completedFuture(IPolicyRemediator.success_(getPolicyId(), "No restricted roles found to remove from policy: " + policy.identity, List.of(paramChange)));
        }

        logger.info("Removing {} restricted roles from policy '{}': {}", rolesToRemove.size(), policy.identity, rolesToRemove);

        // Create futures for removing each restricted role
        List<CompletableFuture<Boolean>> removalFutures = rolesToRemove.stream()
                .map(role -> removeRoleFromPolicy(policy.identity, role))
                .toList();

        return CompletableFuture.allOf(removalFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<Boolean> results = removalFutures.stream()
                            .map(CompletableFuture::join)
                            .toList();

                    boolean allSuccessful = results.stream().allMatch(Boolean::booleanValue);
                    long successCount = results.stream().mapToLong(success -> success ? 1 : 0).sum();

                    ParameterChangeResult paramChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(ParameterEncoder.encode(policy.identity, rolesToRemove))
                            .prevValue(String.join(",", prevRoles))
                            .newValue(String.join(",", newRoles))
                            .status(allSuccessful ? ParameterChangeStatus.SUCCESS : ParameterChangeStatus.FAILED);

                    if (allSuccessful) {
                        logger.info("Successfully removed all {} restricted roles from policy '{}'", rolesToRemove.size(), policy.identity);
                        return IPolicyRemediator.success_(getPolicyId(), "Successfully removed all " + rolesToRemove.size() + " restricted roles from policy '" + policy.identity + "'", List.of(paramChange));
                    } else {
                        String message = String.format("Failed to remove %d out of %d restricted roles from policy '%s'", 
                                rolesToRemove.size() - successCount, rolesToRemove.size(), policy.identity);
                        logger.warn(message);
                        return IPolicyRemediator.failed_(getPolicyId(), message, List.of(paramChange));
                    }
                })
                .exceptionally(ex -> {
                    logger.error("Failed to remove restricted roles from policy '{}': {}", policy.identity, ex.getMessage());
                    ParameterChangeResult paramChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(ParameterEncoder.encode(policy.identity, Collections.emptyList()))
                            .prevValue(String.join(",", prevRoles))
                            .newValue(String.join(",", prevRoles)) // No change due to failure
                            .status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to remove restricted roles from policy '" + policy.identity + "': " + ex.getMessage(), List.of(paramChange));
                });
    }

    private CompletableFuture<Boolean> removeRoleFromPolicy(String policyIdentity, String roleName) {
        return removeRoleFromPolicyWithRetry(policyIdentity, roleName, 3);
    }

    private CompletableFuture<Boolean> removeRoleFromPolicyWithRetry(String policyIdentity, String roleName, int maxRetries) {
        logger.debug("Removing role '{}' from policy '{}' (attempt {}/{})", roleName, policyIdentity, 4 - maxRetries, 3);

        // First, get the management role assignment
        Map<String, Object> getParameters = Map.of(
                ExoConstants.ROLE_ASSIGNEE, policyIdentity,
                ExoConstants.ROLE, roleName,
                "ErrorAction", "SilentlyContinue"
        );

        return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest(ExoConstants.GET_MANAGEMENT_ROLE_ASSIGNMENT, getParameters))
                .thenCompose(getRoleResult -> {
                    // Check if the GET command was successful
                    if (getRoleResult == null) {
                        logger.warn("GET role assignment command returned null for role '{}' and policy '{}'", roleName, policyIdentity);
                        if (maxRetries > 1) {
                            logger.info("Retrying role removal for '{}' from policy '{}' ({} retries left)", roleName, policyIdentity, maxRetries - 1);
                            return removeRoleFromPolicyWithRetry(policyIdentity, roleName, maxRetries - 1);
                        }
                        return CompletableFuture.completedFuture(false);
                    }

                    if (!getRoleResult.isArray() || getRoleResult.isEmpty()) {
                        logger.debug("No role assignment found for role '{}' and policy '{}' - role already removed", roleName, policyIdentity);
                        return CompletableFuture.completedFuture(true); // Consider this a success as the role is already not assigned
                    }

                    logger.info("Found {} role assignments to remove for role '{}' from policy '{}'", getRoleResult.size(), roleName, policyIdentity);

                    // For each role assignment found, remove it
                    List<CompletableFuture<Boolean>> removalFutures = new ArrayList<>();
                    
                    for (JsonNode roleAssignment : getRoleResult) {
                        if (roleAssignment.has(ExoConstants.IDENTITY)) {
                            String assignmentIdentity = roleAssignment.get(ExoConstants.IDENTITY).asText();
                            logger.debug("Removing role assignment: {}", assignmentIdentity);
                            
                            Map<String, Object> removeParameters = Map.of(
                                    ExoConstants.IDENTITY, assignmentIdentity,
                                    "Confirm", false,
                                    "ErrorAction", "Stop"
                            );

                            CompletableFuture<Boolean> removalFuture = exchangeClient.executeCmdletCommand(
                                    new PowerShellClient.CommandRequest(ExoConstants.REMOVE_MANAGEMENT_ROLE_ASSIGNMENT, removeParameters))
                                    .thenApply(removeResult -> {
                                        // Check if the removal command was successful
                                        if (removeResult != null && !removeResult.has("error")) {
                                            logger.info("✅ Successfully removed role assignment '{}' for role '{}' from policy '{}'", 
                                                    assignmentIdentity, roleName, policyIdentity);
                                            return true;
                                        } else {
                                            String errorMsg = removeResult != null && removeResult.has("error") 
                                                    ? removeResult.get("error").asText() 
                                                    : "Unknown error";
                                            logger.error("❌ Failed to remove role assignment '{}': {}", assignmentIdentity, errorMsg);
                                            return false;
                                        }
                                    })
                                    .exceptionally(ex -> {
                                        logger.error("❌ Exception removing role assignment '{}' for role '{}' from policy '{}': {}", 
                                                assignmentIdentity, roleName, policyIdentity, ex.getMessage());
                                        return false;
                                    });
                            
                            removalFutures.add(removalFuture);
                        } else {
                            logger.warn("Role assignment missing 'Identity' field: {}", roleAssignment);
                        }
                    }

                    // Wait for all removals to complete and check results
                    return CompletableFuture.allOf(removalFutures.toArray(new CompletableFuture[0]))
                            .thenApply(v -> {
                                List<Boolean> results = removalFutures.stream()
                                        .map(CompletableFuture::join)
                                        .toList();
                                boolean allSuccessful = results.stream().allMatch(Boolean::booleanValue);
                                long successCount = results.stream().mapToLong(success -> success ? 1 : 0).sum();
                                
                                if (allSuccessful) {
                                    logger.info("✅ Successfully removed all {} role assignments for role '{}' from policy '{}'", 
                                            results.size(), roleName, policyIdentity);
                                    return true;
                                } else {
                                    logger.error("❌ Failed to remove {}/{} role assignments for role '{}' from policy '{}'", 
                                            results.size() - successCount, results.size(), roleName, policyIdentity);
                                    return false;
                                }
                            });
                })
                .handle((success, throwable) -> {
                    if (throwable != null) {
                        logger.error("❌ Exception during role removal for role '{}' and policy '{}': {}", 
                                roleName, policyIdentity, throwable.getMessage());
                        
                        // Retry if we have retries left
                        if (maxRetries > 1) {
                            logger.info("Retrying role removal for '{}' from policy '{}' ({} retries left) due to exception", 
                                    roleName, policyIdentity, maxRetries - 1);
                            return removeRoleFromPolicyWithRetry(policyIdentity, roleName, maxRetries - 1);
                        }
                        return CompletableFuture.completedFuture(false);
                    } else {
                        // If operation failed and we have retries left, retry
                        if (!success && maxRetries > 1) {
                            logger.info("Retrying role removal for '{}' from policy '{}' ({} retries left)", 
                                    roleName, policyIdentity, maxRetries - 1);
                            return removeRoleFromPolicyWithRetry(policyIdentity, roleName, maxRetries - 1);
                        }
                        return CompletableFuture.completedFuture(success);
                    }
                })
                .thenCompose(future -> future);
    }

    private CompletableFuture<PolicyChangeResult> restoreRolesToPolicy(String policyIdentity, List<String> rolesToRestore, 
                                                                     List<String> currentRoles, List<String> targetRoles, 
                                                                     String changeParameter) {
        logger.info("Restoring {} roles to policy '{}': {}", rolesToRestore.size(), policyIdentity, rolesToRestore);

        // Create futures for adding each role back to the policy
        List<CompletableFuture<Boolean>> restorationFutures = rolesToRestore.stream()
                .map(role -> addRoleToPolicy(policyIdentity, role))
                .toList();

        return CompletableFuture.allOf(restorationFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<Boolean> results = restorationFutures.stream()
                            .map(CompletableFuture::join)
                            .toList();

                    boolean allSuccessful = results.stream().allMatch(Boolean::booleanValue);
                    long successCount = results.stream().mapToLong(success -> success ? 1 : 0).sum();

                    ParameterChangeResult rollbackChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(ParameterEncoder.encode(policyIdentity, rolesToRestore))
                            .prevValue(String.join(",", currentRoles)) // Current state before rollback
                            .newValue(String.join(",", targetRoles))   // Target state after rollback
                            .status(allSuccessful ? ParameterChangeStatus.SUCCESS : ParameterChangeStatus.FAILED);

                    if (allSuccessful) {
                        logger.info("Successfully restored all {} roles to policy '{}'", rolesToRestore.size(), policyIdentity);
                        return IPolicyRemediator.success_(getPolicyId(), 
                                "Successfully restored all " + rolesToRestore.size() + " roles to policy '" + policyIdentity + "'", 
                                List.of(rollbackChange));
                    } else {
                        String message = String.format("Failed to restore %d out of %d roles to policy '%s'", 
                                rolesToRestore.size() - successCount, rolesToRestore.size(), policyIdentity);
                        logger.warn(message);
                        return IPolicyRemediator.failed_(getPolicyId(), message, List.of(rollbackChange));
                    }
                })
                .exceptionally(ex -> {
                    logger.error("Failed to restore roles to policy '{}': {}", policyIdentity, ex.getMessage());
                    ParameterChangeResult rollbackChange = new ParameterChangeResult()
                            .timeStamp(Instant.now())
                            .parameter(ParameterEncoder.encode(policyIdentity, Collections.emptyList()))
                            .prevValue(String.join(",", currentRoles))
                            .newValue(String.join(",", currentRoles)) // No change due to failure
                            .status(ParameterChangeStatus.FAILED);
                    return IPolicyRemediator.failed_(getPolicyId(), 
                            "Failed to restore roles to policy '" + policyIdentity + "': " + ex.getMessage(), 
                            List.of(rollbackChange));
                });
    }

    private CompletableFuture<Boolean> addRoleToPolicy(String policyIdentity, String roleName) {
        logger.debug("Adding role '{}' back to policy '{}'", roleName, policyIdentity);

        Map<String, Object> addParameters = Map.of(
                ExoConstants.ROLE, roleName,
                ExoConstants.POLICY, policyIdentity,
                "ErrorAction", "Stop"
        );

        return exchangeClient.executeCmdletCommand(new PowerShellClient.CommandRequest("New-ManagementRoleAssignment", addParameters))
                .thenApply(result -> {
                    logger.debug("Successfully added role '{}' back to policy '{}'", roleName, policyIdentity);
                    return true;
                })
                .exceptionally(ex -> {
                    logger.warn("Failed to add role '{}' back to policy '{}': {}", roleName, policyIdentity, ex.getMessage());
                    return false;
                });
    }

    private CompletableFuture<PolicyChangeResult> combineResults(List<CompletableFuture<PolicyChangeResult>> futures) {
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<PolicyChangeResult> results = futures.stream()
                            .map(CompletableFuture::join)
                            .toList();

                    List<ParameterChangeResult> allChanges = results.stream()
                            .map(PolicyChangeResult::getChanges)
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .filter(Objects::nonNull)
                            .toList();

                    // Count results by type
                    long successCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.SUCCESS)
                            .count();
                    long failedCount = results.stream()
                            .filter(r -> r.getResult() == RemediationResult.FAILED)
                            .count();

                    // Determine overall result status
                    if (failedCount == 0) {
                        // Get the actual roles that were removed from the changes
                        Set<String> actuallyRemovedRoles = allChanges.stream()
                                .filter(change -> change.getParameter().contains("removed_roles:"))
                                .map(change -> {
                                    String param = change.getParameter();
                                    int rolesIndex = param.indexOf("removed_roles:");
                                    if (rolesIndex >= 0) {
                                        return param.substring(rolesIndex + "removed_roles:".length());
                                    }
                                    return "";
                                })
                                .flatMap(rolesStr -> Arrays.stream(rolesStr.split(",")))
                                .map(String::trim)
                                .filter(role -> !role.isEmpty())
                                .collect(Collectors.toSet());
                        
                        String actualRolesStr = actuallyRemovedRoles.isEmpty() 
                                ? String.join(", ", getRestrictedRoleNames()) // Fallback to discovered roles if parsing fails
                                : String.join(", ", actuallyRemovedRoles);
                        
                        return IPolicyRemediator.success_(getPolicyId(), 
                            "Removed restricted roles (" + actualRolesStr + ") from all " + successCount + " role assignment policies to prevent user add-in installation", 
                            allChanges);
                    } else if (successCount == 0) {
                        return IPolicyRemediator.failed_(getPolicyId(), 
                            "Failed to remove restricted roles from all " + failedCount + " policies", 
                            allChanges);
                    } else {
                        return IPolicyRemediator.partial_success_(getPolicyId(),
                                "Fixed " + successCount + " policies, failed to fix " + failedCount + " policies",
                                allChanges);
                    }
                });
    }

    private List<RoleAssignmentPolicy> getRoleAssignmentPolicies() {
        logger.info("Retrieving role assignment policies for add-in compliance audit from configuration.");
        JsonNode auditNode = this.configNode.get(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT);

        if (auditNode == null || !auditNode.isObject()) {
            logger.error("Role assignment audit node not found or not an object in the configuration node.");
            return Collections.emptyList();
        }

        // Extract policies from the enhanced structure
        JsonNode policiesNode = auditNode.get("policies");
        if (policiesNode == null || !policiesNode.isArray()) {
            logger.error("Role assignment policies not found or not an array in the enhanced configuration structure.");
            return Collections.emptyList();
        }

        try {
            CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, RoleAssignmentPolicy.class);
            List<RoleAssignmentPolicy> result = jsonMapper.convertValue(policiesNode, collectionType);
            logger.debug("Successfully converted {} policies from enhanced configuration.", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Failed to convert role assignment policies from enhanced configuration node.", e);
            return Collections.emptyList();
        }
    }

    private boolean hasRestrictedRoles(RoleAssignmentPolicy policy) {
        if (policy.assignedRoles == null || policy.assignedRoles.isEmpty()) {
            logger.debug("Policy '{}' has no assigned roles", policy.identity);
            return false;
        }
        
        Set<String> restrictedRoles = getRestrictedRoleNames();
        
        logger.debug("Checking policy '{}' with {} roles", policy.identity, policy.assignedRoles.size());
        logger.debug("Policy assigned roles: {}", policy.assignedRoles);
        logger.debug("Restricted roles to check: {}", restrictedRoles);
        
        // Check each role individually for debugging
        for (String role : policy.assignedRoles) {
            boolean isRestricted = restrictedRoles.contains(role);
            logger.debug("Role '{}' (length: {}) is restricted: {}", role, role.length(), isRestricted);
        }
        
        boolean hasRestricted = policy.assignedRoles.stream().anyMatch(restrictedRoles::contains);
        logger.info("Policy '{}' has restricted roles: {}", policy.identity, hasRestricted);
        
        return hasRestricted;
    }

    private List<String> removeRestrictedRoles(List<String> currentRoles) {
        if (currentRoles == null) {
            return new ArrayList<>();
        }
        Set<String> restrictedRoles = getRestrictedRoleNames();
        return currentRoles.stream()
                .filter(role -> !restrictedRoles.contains(role))
                .toList();
    }

    /**
     * Gets the restricted role names from the discovered roles in configNode.
     * Falls back to hardcoded values if discovery data is not available.
     */
    private Set<String> getRestrictedRoleNames() {
        JsonNode auditNode = this.configNode.get(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT);
        
        if (auditNode != null && auditNode.isObject()) {
            JsonNode discoveredRolesNode = auditNode.get("discovered_roles");
            
            if (discoveredRolesNode != null && discoveredRolesNode.isObject()) {
                Set<String> discoveredRoles = new HashSet<>();
                
                // Extract role display names from the discovered roles mapping
                discoveredRolesNode.fields().forEachRemaining(entry -> {
                    String roleDisplayName = entry.getValue().asText();
                    if (roleDisplayName != null && !roleDisplayName.trim().isEmpty()) {
                        discoveredRoles.add(roleDisplayName);
                        logger.debug("Using discovered role: {} (from RoleType: {})", roleDisplayName, entry.getKey());
                    }
                });
                
                if (!discoveredRoles.isEmpty()) {
                    logger.info("Using {} discovered restricted roles: {}", discoveredRoles.size(), discoveredRoles);
                    return discoveredRoles;
                }
            }
        }
        
        // Fallback to hardcoded English role names if discovery failed
        Set<String> fallbackRoles = Set.of(
            "My Custom Apps",
            "My Marketplace Apps", 
            "My ReadWriteMailbox Apps"
        );
        logger.warn("Discovery data not available, using fallback restricted roles: {}", fallbackRoles);
        return fallbackRoles;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class RoleAssignmentPolicy {
        @JsonProperty("identity")
        public String identity;
        
        @JsonProperty("assignedRoles")  
        public List<String> assignedRoles;

        //for JsonMapper
        @SuppressWarnings("unused")
        public RoleAssignmentPolicy() {
        }

        public RoleAssignmentPolicy(String identity, List<String> assignedRoles) {
            this.identity = identity;
            this.assignedRoles = assignedRoles != null ? new ArrayList<>(assignedRoles) : new ArrayList<>();
        }
    }

    /**
     * Helper class for robust parameter encoding/decoding to handle policy names
     * that may contain special characters like colons or commas.
     */
    private static class ParameterEncoder {
        private static final String DELIMITER = "||";
        private static final String KEY_VALUE_SEPARATOR = "=";
        
        /**
         * Encodes parameter data into a robust format that can handle special characters.
         * Format: key1=value1||key2=value2||key3=value3
         */
        public static String encode(String policyIdentity, List<String> removedRoles) {
            Map<String, String> params = new HashMap<>();
            params.put("identity", escapeValue(policyIdentity));
            if (removedRoles != null && !removedRoles.isEmpty()) {
                params.put("removed_roles", escapeValue(String.join(",", removedRoles)));
            }
            
            return params.entrySet().stream()
                .map(entry -> entry.getKey() + KEY_VALUE_SEPARATOR + entry.getValue())
                .collect(Collectors.joining(DELIMITER));
        }
        
        /**
         * Decodes parameter string back into a map of key-value pairs.
         */
        public static Map<String, String> decode(String parameterString) {
            Map<String, String> result = new HashMap<>();
            
            if (parameterString == null || parameterString.trim().isEmpty()) {
                return result;
            }
            
            String[] pairs = parameterString.split(Pattern.quote(DELIMITER));
            for (String pair : pairs) {
                String[] keyValue = pair.split(KEY_VALUE_SEPARATOR, 2);
                if (keyValue.length == 2) {
                    result.put(keyValue[0].trim(), unescapeValue(keyValue[1].trim()));
                }
            }
            
            return result;
        }
        
        /**
         * Escape special characters in values to prevent parsing issues.
         */
        private static String escapeValue(String value) {
            if (value == null) return "";
            return value.replace(DELIMITER, "\\|\\|")
                       .replace(KEY_VALUE_SEPARATOR, "\\=");
        }
        
        /**
         * Unescape special characters in values.
         */
        private static String unescapeValue(String value) {
            if (value == null) return "";
            return value.replace("\\|\\|", DELIMITER)
                       .replace("\\=", KEY_VALUE_SEPARATOR);
        }
    }
}