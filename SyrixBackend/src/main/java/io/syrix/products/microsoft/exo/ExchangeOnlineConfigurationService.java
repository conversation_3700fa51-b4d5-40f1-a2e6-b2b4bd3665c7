package io.syrix.products.microsoft.exo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.constants.Constants;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.defender.DefenderConstants;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.dns.DnsService;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.model.GraphRequest;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static io.syrix.common.constants.Constants.DISPLAY_NAME_FIELD;
import static io.syrix.common.constants.Constants.ERROR_FIELD;
import static io.syrix.common.constants.Constants.ID_FIELD;
import static io.syrix.common.constants.Constants.IS_VERIFIED;
import static io.syrix.common.constants.Constants.MESSAGE_FIELD;
import static io.syrix.common.constants.Constants.STATUS_FIELD;
import static io.syrix.common.constants.Constants.USER_PRINCIPAL_NAME;
import static io.syrix.protocols.utils.ProtocolConstants.PARAM_SELECT;

public class ExchangeOnlineConfigurationService extends BaseConfigurationService {

	public static final String GET_MAILBOX_CMD = "Get-Mailbox";
	public static final String EXTERNAL_DIRECTORY_OBJECT_ID = "ExternalDirectoryObjectId";
	public static final String ACCOUNT_ENABLED = "accountEnabled";
	public static final String IS_ENABLED = "isEnabled";

	// Alerts configuration constants

	public static final String ALERTS_CONFIG_FILE = "config/exchange-alerts-config.json";
	public static final String CPPS_ENDPOINT_FILE = "config/exchange-cpps-endpoint.json";
	private final DnsService dnsService;

	private final PowerShellClient powerShellClient;

	public ExchangeOnlineConfigurationService(
			MicrosoftGraphClient graphClient,
			PowerShellClient powershellClient,
			ObjectMapper objectMapper,
			MetricsCollector metrics,
			DnsService dnsService) {
		super(graphClient, objectMapper, metrics);
		this.dnsService = dnsService;
		this.powerShellClient = powershellClient;

	}

	@Override
	public ConfigurationResult exportConfiguration() {
		Instant startTime = Instant.now();
		metrics.recordExportStart();
		logger.info("Starting Exchange Online configuration export at {}", startTime);

		try {
			Map<String, CompletableFuture<?>> futures = new HashMap<>();
			CompletableFuture<JsonNode> acceptedDomainsFeature = getAcceptedDomains();
			// DNS authentication records
			futures.put("spf_records", dnsService.getDomainSpfRecords(acceptedDomainsFeature, this.successfulCommands, this.unsuccessfulCommands));
			// Basic Exchange settings
			futures.put(ExoConstants.CONFIG_KEY_REMOTE_DOMAINS, getRemoteDomains());
			futures.put(ExoConstants.CONFIG_KEY_TRANSPORT, getTransportConfig());
			futures.put(ExoConstants.CONFIG_KEY_SHARING_POLICY, getSharingPolicy());
			futures.put(ExoConstants.CONFIG_KEY_TRANSPORT_RULE, getTransportRules());
			futures.put(ExoConstants.CONFIG_KEY_CONN_FILTER, getConnectionFilter());
			CompletableFuture<JsonNode> orgConfigFuture = getOrganizationConfig();
			futures.put(ExoConstants.CONFIG_KEY_ORGANIZATION, orgConfigFuture);
			futures.put("accepted_domain", acceptedDomainsFeature);
			// Get domains for DNS checks
			CompletableFuture<JsonNode> dkimRecords = dnsService.getDomainDkimRecords(acceptedDomainsFeature,
					this.successfulCommands,
					this.unsuccessfulCommands);
			futures.put("dkim_records", dkimRecords);
			futures.put(ExoConstants.CONFIG_KEY_DKIM_CONFIG, getDKIMConfig());
			futures.put("dmarc_records", dnsService.getDomainDmarcRecords(acceptedDomainsFeature, this.successfulCommands, this.unsuccessfulCommands));
			futures.put(ExoConstants.CONFIG_KEY_SHARED_MAILBOX, getSharedMailboxesWithSignInEnabled());
			futures.put("lockbox_config", auditCustomerLockbox(orgConfigFuture));

			futures.put(ExoConstants.CONFIG_KEY_MALWARE_POLICY, getMalwarePolicy());
			futures.put(ExoConstants.CONFIG_KEY_MALWARE_RULE, getMalwareRules());
			futures.put(ExoConstants.CONFIG_KEY_ANTI_PHISH_POLICY, getAntiPhishPolicy());
			futures.put(ExoConstants.CONFIG_KEY_ANTI_PHISH_RULE, getAntiPhishRule());
			futures.put(ExoConstants.CONFIG_KEY_CONTENT_FILTER_POLICY, getContentFilterPolicy());
			futures.put(ExoConstants.CONFIG_KEY_SAFE_LINKS_POLICY, getAllSafeLinksPolicies());
			futures.put(ExoConstants.CONFIG_KEY_SAFE_LINKS_RULE, getSafeLinksRules());
			futures.put(ExoConstants.CONFIG_KEY_ALERTS, getAlerts());
			futures.put(ExoConstants.CONFIG_KEY_ROLE_ASSIGNMENT_ADDIN_AUDIT, getRoleAssignmentPoliciesForAddInCompliance());

			ConfigurationResult result = waitForFutures(futures).thenApply(map -> {
				map.put("exo_successful_commands", getSuccessfulCommands());
				map.put("exo_unsuccessful_commands", getUnsuccessfulCommands());
				return buildConfigurationResult(map, SERVICE_VERSION, ConfigurationServiceType.EXCHANGE_ONLINE);
			}).get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

			logger.info("Exchange Online configuration export completed successfully");
			return result;

		} catch (Exception e) {
			throw new ConfigurationExportException("Exchange Online configuration export failed", e);
		}
	}

	/**
	 * Retrieves all Safe Links policies from Exchange Online.
	 *
	 * @return A future containing the Safe Links policies as JSON
	 */
	public CompletableFuture<JsonNode> getAllSafeLinksPolicies() {
		PowerShellClient.CommandRequest safeLinksPolicyRequest = new PowerShellClient.CommandRequest("Get-SafeLinksPolicy", Map.of());

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(safeLinksPolicyRequest), safeLinksPolicyRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getAllSafeLinksPolicies {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	public CompletableFuture<JsonNode> getSafeLinksRules() {
		PowerShellClient.CommandRequest safeLinksRuleRequest = new PowerShellClient.CommandRequest("Get-SafeLinksRule", Map.of());
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(safeLinksRuleRequest), safeLinksRuleRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getAllSafeLinksPolicies {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getMalwarePolicy() {
		PowerShellClient.CommandRequest malwarePolicy = new PowerShellClient.CommandRequest(DefenderConstants.GET_MALWARE_FILTER_POLICY, Map.of());

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(malwarePolicy), malwarePolicy.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getMalwarePolicy {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getMalwareRules() {
		PowerShellClient.CommandRequest malwareRules = new PowerShellClient.CommandRequest(DefenderConstants.GET_MALWARE_RULES, Map.of());

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(malwareRules), malwareRules.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getMalwareRules {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves anti-phishing policies from Exchange Online.
	 *
	 * @return A future containing the anti-phishing policies as JSON
	 */
	private CompletableFuture<JsonNode> getAntiPhishPolicy() {
		PowerShellClient.CommandRequest antiPhishPolicyRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_ANTI_PHISH_POLICY, Map.of());
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(antiPhishPolicyRequest), antiPhishPolicyRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getAntiPhishPolicy {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves anti-phishing rules from Exchange Online.
	 *
	 * @return A future containing the anti-phishing rules as JSON
	 */
	private CompletableFuture<JsonNode> getAntiPhishRule() {
		PowerShellClient.CommandRequest antiPhishRuleRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_ANTI_PHISH_RULE, Map.of());
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(antiPhishRuleRequest), antiPhishRuleRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getAntiPhishRule {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves the content filter policy from Exchange Online.
	 *
	 * @return A future containing the content filter policy as JSON
	 */
	public CompletableFuture<JsonNode> getContentFilterPolicy() {
		PowerShellClient.CommandRequest contentFilterRequest =
				new PowerShellClient.CommandRequest(ExoConstants.GET_HOSTED_CONTENT_FILTER_POLICY, Map.of("Identity", "Default"));

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(contentFilterRequest), contentFilterRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getContentFilterPolicy: {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}


	private CompletableFuture<?> getDKIMConfig() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_DKIM_SIGNING_CONFIG, PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getDKIMConfig {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	public CompletableFuture<JsonNode> getAcceptedDomains() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest("Get-AcceptedDomain", PowerShellClient.DEFAULT_PARAMETERS);

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e ->
						// If PowerShell command fails, fallback to Graph API
						graphClient.makeGraphRequest(
								GraphRequest.builder()
										.beta()
										.withEndpoint(Constants.DOMAINS_ENDPOINT)
										.build()
						).thenApply(domains -> {
							try {
								// Filter domains by isVerified=true
								return JsonDomainProcessor.filterByBoolean(domains, IS_VERIFIED, true, objectMapper);
							} catch (Exception ex) {
								logger.error("Failed to filter domains: {}", ex.getMessage());
								return objectMapper.createArrayNode();
							}
						}).exceptionally(ex -> {
							logger.error("Failed to call getAcceptedDomains {}", ex.getMessage());
							return objectMapper.createArrayNode();
						}).join() // Join needed here since we're inside exceptionally
				);
	}


	private CompletableFuture<JsonNode> getRemoteDomains() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_REMOTE_DOMAIN, PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getRemoteDomains {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getTransportConfig() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_TRANSPORT_CONFIG, PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getTransportConfig {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getSharingPolicy() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest("Get-SharingPolicy", PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getSharingPolicy {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getTransportRules() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest("Get-TransportRule", PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getTransportRules {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	private CompletableFuture<JsonNode> getConnectionFilter() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_HOSTED_CONNECTION_FILTER_POLICY, PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getConnectionFilter {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	public CompletableFuture<JsonNode> getOrganizationConfig() {
		PowerShellClient.CommandRequest remoteDomainRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_ORGANIZATION_CONFIG, PowerShellClient.DEFAULT_PARAMETERS);
		return withRetry(() -> this.powerShellClient.executeCmdletCommand(remoteDomainRequest), remoteDomainRequest.getCmdletName())
				.exceptionally(e -> {
					logger.error("Failed to call getOrganizationConfig {}", e.getMessage());
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Audits the Customer Lockbox settings for the organization
	 * This implements the security requirement for Customer Lockbox
	 *
	 * @return CompletableFuture with audit results containing Customer Lockbox status
	 */
	public CompletableFuture<JsonNode> auditCustomerLockbox(CompletableFuture<JsonNode> orgConfigFuture) {
		logger.info("Auditing Customer Lockbox configuration");

		return orgConfigFuture.thenApply(result -> {
			JsonNode orgConfig = null;

			// Handle array or object response
			if (result.isArray() && !result.isEmpty()) {
				orgConfig = result.get(0);
			} else if (result.isObject()) {
				orgConfig = result;
			}

			if (orgConfig == null) {
				logger.error("Failed to get organization configuration");
				return createErrorNode("Failed to retrieve Customer Lockbox configuration");
			}

			ObjectNode auditResult = objectMapper.createObjectNode();
			if (orgConfig.has(ExoConstants.CUSTOMER_LOCKBOX_ENABLED)) {
				boolean isEnabled = orgConfig.get(ExoConstants.CUSTOMER_LOCKBOX_ENABLED).asBoolean();
				auditResult.put(IS_ENABLED, isEnabled);
			} else {
				auditResult.put(STATUS_FIELD, "unknown");
				auditResult.put(MESSAGE_FIELD, "Could not determine Customer Lockbox status");
			}

			return auditResult;
		}).exceptionally(e -> {
			logger.error("Failed to audit Customer Lockbox: {}", e.getMessage(), e);
			return createErrorNode("Error auditing Customer Lockbox: " + e.getMessage());
		});
	}

	private JsonNode createErrorNode(String message) {
		ObjectNode errorNode = objectMapper.createObjectNode();
		errorNode.put(STATUS_FIELD, ERROR_FIELD);
		errorNode.put(MESSAGE_FIELD, message);
		return errorNode;
	}

	/**
	 * Gets shared mailboxes that have sign-ins enabled.
	 * Implements the check for shared mailboxes with sign-in allowed.
	 *
	 * @return CompletableFuture with array of shared mailboxes with enabled sign-ins
	 */
	public CompletableFuture<ArrayNode> getSharedMailboxesWithSignInEnabled() {
		logger.info("Checking for shared mailboxes with sign-ins enabled");

		// First, get all shared mailboxes using PowerShell cmdlet
		PowerShellClient.CommandRequest getSharedMailboxesRequest =
				new PowerShellClient.CommandRequest(GET_MAILBOX_CMD,
						Map.of("RecipientTypeDetails", "SharedMailbox",
								"ResultSize", Constants.UNLIMITED_VALUE));

		return withRetry(() ->
						this.powerShellClient.executeCmdletCommand(getSharedMailboxesRequest),
				getSharedMailboxesRequest.getCmdletName())
				.thenCompose(sharedMailboxes -> {
					if (sharedMailboxes == null || !sharedMailboxes.isArray() || sharedMailboxes.isEmpty()) {
						logger.info("No shared mailboxes found");
						return CompletableFuture.completedFuture(objectMapper.createArrayNode());
					}

					// Process each shared mailbox to check if sign-in is enabled
					List<CompletableFuture<JsonNode>> mailboxFutures = new ArrayList<>();

					for (JsonNode mailbox : sharedMailboxes) {
						String externalDirectoryObjectId = mailbox.has(EXTERNAL_DIRECTORY_OBJECT_ID) ?
								mailbox.get(EXTERNAL_DIRECTORY_OBJECT_ID).asText() : null;

						if (externalDirectoryObjectId == null) {
							continue;
						}

						// Get the account enabled status using Graph API
						CompletableFuture<JsonNode> userFuture = withRetry(() ->
										graphClient.makeGraphRequest(GraphRequest.builder()
												.beta()
												.withEndpoint("/users/" + externalDirectoryObjectId)
												.addQueryParam(PARAM_SELECT, "id,displayName,userPrincipalName,accountEnabled")
												.build()),
								"Get-MgUser");

						CompletableFuture<JsonNode> data = userFuture.thenApply(user -> {
							// Check if account is enabled
							if (user.has(ACCOUNT_ENABLED)) {
								ObjectNode enabledMailbox = objectMapper.createObjectNode();
								enabledMailbox.put(ID_FIELD, user.get(ID_FIELD).asText());
								enabledMailbox.put(DISPLAY_NAME_FIELD, user.get(DISPLAY_NAME_FIELD).asText());
								enabledMailbox.put(USER_PRINCIPAL_NAME, user.get(USER_PRINCIPAL_NAME).asText());
								enabledMailbox.put(ACCOUNT_ENABLED, user.get(ACCOUNT_ENABLED).asBoolean());
								return (JsonNode) enabledMailbox;
							}
							return null;
						}).exceptionally(ex -> {
							logger.error("Error checking account status for mailbox: {}",
									mailbox.has(DISPLAY_NAME_FIELD) ? mailbox.get(DISPLAY_NAME_FIELD).asText() : externalDirectoryObjectId,
									ex);
							return null;
						});
						data.join();
						mailboxFutures.add(data);
					}

					// Combine all results
					return CompletableFuture.allOf(mailboxFutures.toArray(new CompletableFuture[0]))
							.thenApply(v -> {
								ArrayNode enabledMailboxes = objectMapper.createArrayNode();
								mailboxFutures.stream()
										.map(CompletableFuture::join)
										.filter(Objects::nonNull)
										.forEach(enabledMailboxes::add);

								logger.info("Found {} shared mailboxes with sign-ins enabled", enabledMailboxes.size());
								return enabledMailboxes;
							});
				})
				.exceptionally(e -> {
					logger.error("Failed to check shared mailboxes: {}", e.getMessage(), e);
					return objectMapper.createArrayNode();
				});
	}

	public CompletableFuture<JsonNode> getAlerts() {
		return powerShellClient.getCPPSEndpoint().thenCompose(cppsEndpoint -> {
					PowerShellClient.CommandRequest alertsRequest = new PowerShellClient.CommandRequest(ExoConstants.GET_PROTECTION_ALERT, new HashMap<>(), cppsEndpoint);
					return powerShellClient.executeCmdletCommand(alertsRequest);
				})
				.exceptionally(e -> {
					logger.error("Failed to call ProtectionAlert", e);
					return objectMapper.createArrayNode();
				});
	}

	/**
	 * Retrieves role assignment policies for add-in installation compliance validation.
	 * This method first discovers the actual display names of restricted roles by their RoleType,
	 * then fetches policies that control which roles users can be assigned.
	 
	 * The returned data structure includes:
	 * - discovered_roles: Map of RoleType to actual display names
	 * - policies: Array of role assignment policies with identity and assignedRoles
	 *
	 * @return A future containing enhanced role assignment policies as JSON
	 * @throws RuntimeException if role discovery or policy retrieval fails
	 */
	public CompletableFuture<JsonNode> getRoleAssignmentPoliciesForAddInCompliance() {
		// Step 1: Discover actual role display names by RoleType
		return discoverRestrictedRolesByType()
			.thenCompose(discoveredRoles -> {
				// Step 2: Get role assignment policies
				PowerShellClient.CommandRequest roleAssignmentPolicyRequest =
					new PowerShellClient.CommandRequest(ExoConstants.GET_ROLE_ASSIGNMENT_POLICY, Map.of());
				return withRetry(() -> this.powerShellClient.executeCmdletCommand(roleAssignmentPolicyRequest),
							   roleAssignmentPolicyRequest.getCmdletName())
					.thenApply(policies -> enhanceWithDiscoveredRoles(policies, discoveredRoles));
			});
	}

	/**
	 * Filters role assignment policies to include only fields needed for Rego policy validation.
	 * Extracts Identity and AssignedRoles fields from the PowerShell response and maps them
	 * to the correct field names expected by the RoleAssignmentPolicy class.
	 *
	 * @param response The raw PowerShell response containing role assignment policies
	 * @return Filtered JSON array containing only identity and assignedRoles fields
	 */
	private JsonNode filterRoleAssignmentPoliciesForRegoValidation(JsonNode response) {
		if (!response.isArray()) {
			logger.warn("Expected array response for role assignment policies, got: {}", response.getNodeType());
			return objectMapper.createArrayNode();
		}

		ArrayNode filteredPolicies = objectMapper.createArrayNode();

		for (JsonNode policy : response) {
			ObjectNode filteredPolicy = objectMapper.createObjectNode();
			
			// Extract fields needed by Rego policy validation and map to correct field names
			if (policy.has(ExoConstants.IDENTITY)) {
				// Map PowerShell "Identity" to Java class field "identity" (lowercase)
				filteredPolicy.set("identity", policy.get(ExoConstants.IDENTITY));
			}
			
			if (policy.has(ExoConstants.ASSIGNED_ROLES)) {
				// Map PowerShell "AssignedRoles" to Java class field "assignedRoles" (camelCase)
				filteredPolicy.set("assignedRoles", policy.get(ExoConstants.ASSIGNED_ROLES));
			}
			
			filteredPolicies.add(filteredPolicy);
		}

		logger.debug("Filtered {} role assignment policies for add-in compliance validation", filteredPolicies.size());
		return filteredPolicies;
	}

	/**
	 * Discovers restricted roles by their RoleType to get language-independent role identification.
	 * Uses Get-ManagementRole to find roles with specific RoleTypes that allow add-in installation.
	 *
	 * @return A future containing a map of RoleType to actual display names
	 * @throws RuntimeException if role discovery fails
	 */
	private CompletableFuture<Map<String, String>> discoverRestrictedRolesByType() {
		// Target role types that allow add-in installation
		List<String> targetRoleTypes = List.of("MyCustomApps", "MyMarketplaceApps", "MyReadWriteMailboxApps");

		// PowerShell: Get-ManagementRole | Where-Object { @('MyCustomApps','MyMarketplaceApps','MyReadWriteMailboxApps') -contains $_.RoleType }
		Map<String, Object> params = Map.of();
		PowerShellClient.CommandRequest roleDiscoveryRequest =
			new PowerShellClient.CommandRequest(ExoConstants.GET_MANAGEMENT_ROLE, params);

		return withRetry(() -> this.powerShellClient.executeCmdletCommand(roleDiscoveryRequest),
					   roleDiscoveryRequest.getCmdletName())
			.thenApply(rolesResponse -> parseDiscoveredRoles(rolesResponse, targetRoleTypes));
	}

	/**
	 * Parses the response from Get-ManagementRole to extract role mappings.
	 *
	 * @param rolesResponse The PowerShell response containing management roles
	 * @param targetRoleTypes The target role types to filter for
	 * @return A map of RoleType to actual display names
	 * @throws RuntimeException if parsing fails or required roles are not found
	 */
	private Map<String, String> parseDiscoveredRoles(JsonNode rolesResponse, List<String> targetRoleTypes) {
		Map<String, String> discoveredRoles = new HashMap<>();

		if (!rolesResponse.isArray()) {
			throw new RuntimeException("Expected array response for management roles, got: " + rolesResponse.getNodeType());
		}

		for (JsonNode role : rolesResponse) {
			if (role.has(ExoConstants.ROLE_TYPE) && role.has(ExoConstants.NAME)) {
				String roleType = role.get(ExoConstants.ROLE_TYPE).asText();
				String roleName = role.get(ExoConstants.NAME).asText();

				// Only include roles that match our target role types
				if (targetRoleTypes.contains(roleType)) {
					discoveredRoles.put(roleType, roleName);
					logger.debug("Discovered role mapping: {} -> {}", roleType, roleName);
				}
			}
		}

		// Verify we found all expected role types
		List<String> missingRoles = new ArrayList<>();
		for (String expectedType : targetRoleTypes) {
			if (!discoveredRoles.containsKey(expectedType)) {
				missingRoles.add(expectedType);
			}
		}

		if (!missingRoles.isEmpty()) {
			throw new RuntimeException("Required role types not found during discovery: " + missingRoles);
		}

		logger.info("Successfully discovered {} restricted role mappings", discoveredRoles.size());
		return discoveredRoles;
	}



	/**
	 * Enhances role assignment policies with discovered role information.
	 * Creates the new data structure with discovered_roles and policies sections.
	 * 
	 * @param policiesResponse The raw PowerShell response containing role assignment policies
	 * @param discoveredRoles The map of RoleType to actual display names
	 * @return Enhanced JSON structure with discovered roles and filtered policies
	 */
	private JsonNode enhanceWithDiscoveredRoles(JsonNode policiesResponse, Map<String, String> discoveredRoles) {
		ObjectNode enhancedConfig = objectMapper.createObjectNode();
		
		// Add discovered roles section
		ObjectNode discoveredRolesNode = objectMapper.createObjectNode();
		discoveredRoles.forEach(discoveredRolesNode::put);
		enhancedConfig.set("discovered_roles", discoveredRolesNode);
		
		// Add filtered policies section
		JsonNode filteredPolicies = filterRoleAssignmentPoliciesForRegoValidation(policiesResponse);
		enhancedConfig.set("policies", filteredPolicies);
		
		logger.debug("Enhanced config with {} discovered roles and {} policies", 
				   discoveredRoles.size(), filteredPolicies.size());
		
		return enhancedConfig;
	}


}