package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import io.syrix.common.constants.Constants;
import io.syrix.common.utils.Utils;
import io.syrix.domain.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.AlertDefinition;
import io.syrix.products.microsoft.base.AlertsConfiguration;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellClient.CommandRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Remediator for MS.EXO.16.1v1 which enables critical security alerts in Microsoft Exchange Online.
 * <p>
 * This class implements the MS.EXO.16.1v1 control to enable required alerts:
 * a. Suspicious email sending patterns detected
 * b. Suspicious Connector Activity
 * c. Suspicious Email Forwarding Activity
 * d. Messages have been delayed
 * e. Tenant restricted from sending unprovisioned email
 * f. Tenant restricted from sending email
 * g. A potentially malicious URL click was detected
 * <p>
 * These alerts help detect potential attacks including:
 * - T1078.004: Cloud Accounts
 * - T1562.006: Indicator Blocking
 * - T1566.002: Spearphishing Link
 */
//@PolicyRemediator("MS.EXO.16.1v1") TODO Artur stop there
public class ExchangeAlertsEnabler extends ExchangeBaseRemediator {

	// Constants for field names and commands
	protected static final String GET_PROTECTION_ALERT = "Get-ProtectionAlert";
	protected static final String NEW_PROTECTION_ALERT = "New-ProtectionAlert";

	// Default settings
	protected static final int MAX_ALERT_NAME_LENGTH = 63;
	protected static final String DEFAULT_NOTIFY_USER = "TenantAdmins";

	// Alert properties
	protected static final String NOTIFY_USER = "NotifyUser";
	protected static final String OPERATION = "Operation";
	protected static final String NOTIFICATION_ENABLED = "NotificationEnabled";
	protected static final String SEVERITY = "Severity";
	protected static final String CATEGORY = "Category";
	protected static final String COMMENT = "Comment";
	protected static final String THREAT_TYPE = "ThreatType";
	protected static final String AGGREGATION_TYPE = "AggregationType";
	protected static final String DISABLED = "Disabled";
	protected static final String DESCRIPTION = Constants.DESCRIPTION_FIELD;
	protected static final String CONDITION = "Condition";

	protected static final String ALERTS_RESOURCES_NAME = "alertsconfig/microsoft-alerts-config.yml";

	// Configuration files
	private static final String ALERTS_CONFIG_FILE = "config/exchange-alerts-config.json";
	private static final String CPPS_ENDPOINT_FILE = "config/exchange-cpps-endpoint.json";

	// Map of required alerts with their display names and internal identifiers
	protected static final List<String> REQUIRED_ALERTS = List.of(
			"Suspicious email sending patterns detected",
			"Suspicious Connector Activity",
			"Suspicious Email Forwarding Activity",
			"Messages have been delayed",
			"Tenant restricted from sending unprovisioned email",
			"Tenant restricted from sending email",
			"A potentially malicious URL click was detected"
	);

	private ArrayList<String> alertsToConfigure_bak;
	private final Set<String> alertsToConfigure;
	protected String csspEndpoint;
	protected final String tenantHashSuffix;
	protected final Set<String> newAlerts = new HashSet<>();

	/**
	 * Constructs a new ExchangeAlertsEnabler with the specified PowerShell client.
	 *
	 * @param graphClient        The Microsoft Graph client
	 * @param exchangeClient     The Exchange Online PowerShell client
	 * @param configNode         Configuration node with tenant data
	 * @param remediationContext The remediation context
	 * @param remediationConfig  The remediation configuration
	 * @param tenantId           The tenant ID to use for creating the hash suffix
	 * @param additionalAlerts   Additional alerts to configure
	 */
	public ExchangeAlertsEnabler(MicrosoftGraphClient graphClient,
								 PowerShellClient exchangeClient,
								 ObjectNode configNode,
								 ExchangeRemediationContext remediationContext,
								 ExchangeRemediationConfig remediationConfig,
								 String tenantId,
								 List<String> additionalAlerts) {
		super(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);

		try {
			this.tenantHashSuffix = Utils.generateMD5Hash(tenantId);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}

		this.alertsToConfigure = new HashSet<>(REQUIRED_ALERTS);
		if (!CollectionUtils.isEmpty(additionalAlerts)) {
			this.alertsToConfigure.addAll(additionalAlerts);
		}
		newAlerts.addAll(alertsToConfigure);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		logger.info("Starting notification configuration for {} - Required Alerts", getPolicyId());

		// Load alert configurations
		final Map<String, AlertDefinition> alertsDefinition = loadAlertConfigurationsMap(ALERTS_RESOURCES_NAME);
		if (alertsDefinition == null) {
			return IPolicyRemediator.failed(getPolicyId(), "Failed to load alert configurations from resources");
		}

		List<AlertConfigItem> alertsItems = loadAlertsFromConfig();
		if (alertsItems == null) {
			return IPolicyRemediator.failed(getPolicyId(), "Failed to load alerts from configuration");
		}

		List<AlertConfigItem> foundItems = alertsItems.stream()
				.filter(alertItem -> alertsDefinition.containsKey(alertItem.name))
				.toList();


		return null;

//		items.stream().map(alertItem -> alertItem.name).toList().forEach(newAlerts::remove);
//

//		return loadCPPSEndpointFromFile()
//				.thenCompose(cppsEndpoint -> {
//					this.csspEndpoint = cppsEndpoint;
//					return loadAlertsFromConfig()
//							.thenCompose(allAlerts -> configureAlerts(allAlerts, alertsDefinition))
//							.thenCompose(result -> {
//								// After configuring existing alerts, check if there are new alerts to create
//								if (newAlerts.isEmpty()) {
//									logger.info("All required alerts were found and configured");
//									return CompletableFuture.completedFuture(result);
//								}
//
//								logger.info("Some required alerts were not found, creating them from definitions");
//								return createNewAlerts(alertsDefinition)
//										.thenApply(createResult -> {
//											// Combine results if both operations were successful
//											if (isSuccessResult(result) && isSuccessResult(createResult)) {
//												return createSuccessNode("Successfully configured existing alerts and created missing alerts");
//											} else if (isSuccessResult(result)) {
//												// Configuration succeeded but creation had issues
//												return createResult;
//											} else if (isSuccessResult(createResult)) {
//												// Creation succeeded but configuration had issues
//												return result;
//											} else {
//												// Both had issues, report both
//												return createFailureNode("Issues occurred during alert configuration and creation");
//											}
//										});
//							})
//							.exceptionally(ex -> {
//								logger.error("Exception during alert notification configuration", ex);
//								return createFailureNode("Failed to configure alert notifications: " + ex.getMessage());
//							});
//				});
	}

	/**
	 * Find existing custom alert that matches the source alert.
	 */
	protected JsonNode findExistingCustomAlert(JsonNode allAlerts, JsonNode sourceAlert, String suffix) {
		if (allAlerts == null || !allAlerts.isArray() || sourceAlert == null) {
			return null;
		}

		String sourceAlertName = sourceAlert.path(Constants.NAME_FIELD).asText();
		String sourceAlertBase = getBaseAlertName(sourceAlertName);
		String newAlertName = generateNewAlertName(sourceAlertName, suffix);

		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			if (alertName.equals(newAlertName)) {
				logger.info("Found alert with exact name '{}'", alertName);
				return alert;
			}

			if (alertName.endsWith(suffix) && alertName.startsWith(sourceAlertBase)) {
				logger.info("Found alert with matching base name and suffix '{}'", alertName);
				return alert;
			}
		}

		List<String> allAlertNames = new ArrayList<>();
		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			allAlertNames.add(alertName);
		}

		if (allAlertNames.contains(newAlertName)) {
			logger.info("Alert name '{}' exists in the list of alerts but wasn't matched in previous search", newAlertName);
			for (JsonNode alert : allAlerts) {
				if (alert.path(Constants.NAME_FIELD).asText().equals(newAlertName)) {
					return alert;
				}
			}
		}

		return null;
	}

	/**
	 * Get the base name of an alert without any suffixes
	 */
	protected String getBaseAlertName(String alertName) {
		if (alertName.contains("-")) {
			return alertName.substring(0, alertName.lastIndexOf("-"));
		}
		return alertName;
	}

	/**
	 * Generate a new alert name with suffix, ensuring it doesn't exceed the maximum length.
	 */
	protected String generateNewAlertName(String originalName, String suffix) {
		int maxBaseLength = MAX_ALERT_NAME_LENGTH - suffix.length();
		String baseName = originalName.length() > maxBaseLength ?
				originalName.substring(0, maxBaseLength) : originalName;
		return baseName + suffix;
	}

	/**
	 * Duplicate a single alert with custom settings or update an existing one.
	 */
	protected CompletableFuture<JsonNode> duplicateOrUpdateAlert(JsonNode sourceAlert, JsonNode allAlerts,
																 String suffix, Map<String, Object> customParameters) {
		String sourceAlertName = sourceAlert.path(Constants.NAME_FIELD).asText();
		String newAlertName = generateNewAlertName(sourceAlertName, suffix);

		logger.info("Checking for existing custom alert for '{}'", sourceAlertName);

		JsonNode existingCustomAlert = findExistingCustomAlert(allAlerts, sourceAlert, suffix);

		if (existingCustomAlert != null) {
			String existingAlertName = existingCustomAlert.path(Constants.NAME_FIELD).asText();
			logger.info("Found existing custom alert '{}', using it instead of creating a new one", existingAlertName);

			return CompletableFuture.completedFuture(existingCustomAlert);
		}

		List<String> allAlertNames = new ArrayList<>();
		for (JsonNode alert : allAlerts) {
			allAlertNames.add(alert.path(Constants.NAME_FIELD).asText());
		}

		if (allAlertNames.contains(newAlertName)) {
			logger.info("Alert with name '{}' already exists but wasn't matched by our custom alert finder", newAlertName);
			for (JsonNode alert : allAlerts) {
				if (alert.path(Constants.NAME_FIELD).asText().equals(newAlertName)) {
					return CompletableFuture.completedFuture(alert);
				}
			}
		}

		return duplicateAlert(sourceAlert, newAlertName, customParameters);
	}

	/**
	 * Duplicate a single alert with custom settings.
	 */
	protected CompletableFuture<JsonNode> duplicateAlert(JsonNode sourceAlert, String targetName, Map<String, Object> customParameters) {
		logger.info("Duplicating alert '{}' as '{}'", sourceAlert.path(Constants.NAME_FIELD).asText(), targetName);

		Map<String, Object> parameters = new HashMap<>();
		parameters.put(Constants.NAME_FIELD, targetName);
		parameters.put(NOTIFICATION_ENABLED, true);
		parameters.put(AGGREGATION_TYPE, "None");
		parameters.put(DISABLED, false);

		// Copy all relevant properties from the source alert
		copyAlertProperty(sourceAlert, parameters, OPERATION);
		copyAlertProperty(sourceAlert, parameters, SEVERITY);
		copyAlertProperty(sourceAlert, parameters, CATEGORY);
		copyAlertProperty(sourceAlert, parameters, COMMENT);
		copyAlertProperty(sourceAlert, parameters, THREAT_TYPE);
		copyAlertProperty(sourceAlert, parameters, DESCRIPTION);
		copyAlertProperty(sourceAlert, parameters, CONDITION);
		copyAlertProperty(sourceAlert, parameters, "Filter");

		String alertName = sourceAlert.path(Constants.NAME_FIELD).asText();

		if (customParameters != null) {
			parameters.putAll(customParameters);
		}

		for (Map.Entry<String, Object> entry : customParameters.entrySet()) {
			if (entry.getValue() instanceof String value && StringUtils.isEmpty(value)) {
				parameters.remove(entry.getKey());
			}
		}

		return exchangeClient.executeCmdletCommand(new CommandRequest(NEW_PROTECTION_ALERT, parameters, this.csspEndpoint))
				.thenApply(result -> {
					if (result == null || result.has(Constants.ERROR_FIELD)) {
						String errorMsg = result != null ?
								extractErrorMessage(result) :
								"Failed to create custom version of alert: " + sourceAlert.path(Constants.NAME_FIELD).asText();
						logger.error(errorMsg);
						return result != null ? result : createErrorNode(errorMsg);
					}

					logger.info("Successfully created custom version of alert '{}' as '{}'", sourceAlert.path(Constants.NAME_FIELD).asText(), targetName);
					return result;
				}).exceptionally(ex -> {
					logger.error("Exception creating custom version of alert: {} with message {}", alertName, ex.getMessage());
					String errorMsg = "Failed to create custom version of alert : " + alertName + " with message : " + ex.getMessage();
					return createErrorNode(errorMsg);
				});
	}

	/**
	 * Загружает CPPS endpoint из JSON файла
	 *
	 * @return CompletableFuture with CPPS endpoint
	 */
	private CompletableFuture<String> loadCPPSEndpointFromFile() {
		try {
			Path configPath = getConfigFilePath(CPPS_ENDPOINT_FILE);
			if (!Files.exists(configPath)) {
				logger.error("CPPS endpoint file not found: {}", configPath.toAbsolutePath());
				return CompletableFuture.failedFuture(new IOException("CPPS endpoint file not found"));
			}

			String jsonContent = Files.readString(configPath);
			JsonNode endpointData = jsonMapper.readTree(jsonContent);

			if (endpointData.has("cppsEndpoint")) {
				String endpoint = endpointData.get("cppsEndpoint").asText();
				logger.info("Loaded CPPS endpoint from file: {}", endpoint);
				return CompletableFuture.completedFuture(endpoint);
			} else {
				logger.error("Invalid CPPS endpoint file format");
				return CompletableFuture.failedFuture(new IOException("Invalid CPPS endpoint file format"));
			}
		} catch (IOException e) {
			logger.error("Failed to load CPPS endpoint from file: {}", e.getMessage());
			return CompletableFuture.failedFuture(e);
		}
	}

	private List<AlertConfigItem> loadAlertsFromConfig() {
		if (configNode == null) {
			logger.warn("Config node is null, returning empty list of Alerts Config");
			return null;
		}

		JsonNode config = configNode.get(ExoConstants.CONFIG_KEY_ALERTS);
		if (config == null) {
			logger.warn("Alerts Configs '{}' node not found", ExoConstants.CONFIG_KEY_ALERTS);
			return null;
		}
		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, AlertConfigItem.class);
		return jsonMapper.convertValue(config, collectionType);
	}

	/**
	 * Получает путь к конфигурационному файлу
	 *
	 * @param fileName имя файла
	 * @return Path к файлу
	 */
	private Path getConfigFilePath(String fileName) {
		// Получаем путь к src/main/resources
		String resourcesPath = "src/main/resources/";
		Path projectRoot = Paths.get(System.getProperty("user.dir"));
		return projectRoot.resolve(resourcesPath).resolve(fileName);
	}

	/**
	 * Helper to copy a property from source alert to parameters map if it exists
	 */
	protected void copyAlertProperty(JsonNode sourceAlert, Map<String, Object> parameters, String propertyName) {
		JsonNode propertyNode = sourceAlert.path(propertyName);
		if (!propertyNode.isMissingNode() && !propertyNode.isNull()) {
			if (propertyNode.isBoolean()) {
				parameters.put(propertyName, propertyNode.asBoolean());
			} else if (propertyNode.isInt()) {
				parameters.put(propertyName, propertyNode.asInt());
			} else if (propertyNode.isTextual()) {
				parameters.put(propertyName, propertyNode.asText());
			} else if (propertyNode.isArray()) {
				List<Object> list = new ArrayList<>();
				propertyNode.forEach(item -> {
					if (item.isTextual()) {
						list.add(item.asText());
					} else if (item.isInt()) {
						list.add(item.asInt());
					} else if (item.isBoolean()) {
						list.add(item.asBoolean());
					} else {
						list.add(item);
					}
				});
				parameters.put(propertyName, list);
			} else {
				parameters.put(propertyName, propertyNode);
			}
		}
	}

	/**
	 * Helper method to check if a result indicates success.
	 */
	private boolean isSuccessResult(JsonNode result) {
		return result != null &&
			   result.has(Constants.STATUS_FIELD) &&
			   result.get(Constants.STATUS_FIELD).asText().equals(Constants.SUCCESS_STATUS);
	}

	/**
	 * Loads alert configurations from a YAML file and builds a mapping of alert IDs to their configurations.
	 */
	protected Map<String, AlertDefinition> loadAlertConfigurationsMap(String resourceName) {
		logger.info("Loading alert configurations from resource: {}", resourceName);

		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourceName);
		if (inputStream == null) {
			logger.error("Resource not found in src/main/resources: {}", resourceName);
			return null;
		}

		try {
			ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
			yamlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

			AlertsConfiguration config = yamlMapper.readValue(inputStream, AlertsConfiguration.class);

			Map<String, AlertDefinition> alertsMap = new HashMap<>();
			if (config.getAlerts() != null) {
				for (AlertDefinition alert : config.getAlerts()) {
					if (alert.getId() != null) {
						alertsMap.put(alert.getName(), alert);
						logger.debug("Loaded alert definition: {}", alert.getId());
					} else {
						logger.warn("Skipping alert definition with null ID: {}", alert.getName());
					}
				}
			}

			logger.info("Loaded {} alert configurations from {}", alertsMap.size(), resourceName);
			return alertsMap;
		} catch (Exception e) {
			logger.error("Error parsing YAML configuration from {}", resourceName, e);
			return null;
		} finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				logger.warn("Error closing input stream", e);
			}
		}
	}

	/**
	 * Enables required alerts by creating custom duplicate versions that are enabled.
	 */
	protected CompletableFuture<JsonNode> configureAlerts(JsonNode allAlerts, Map<String, AlertDefinition> alertsDefinition) {
		if (!isValidAlertResponse(allAlerts)) {
			return CompletableFuture.completedFuture(
					createFailureNode("No protection alerts found.")
			);
		}

		// Lists to track existing alerts and alert statuses
		List<String> existingAlertNames = new ArrayList<>();
		List<JsonNode> requiredAlerts = identifyRequiredAlerts(allAlerts, existingAlertNames);

		if (requiredAlerts.isEmpty()) {
			return CompletableFuture.completedFuture(
					createFailureNode("No matching built-in alert policies found.")
			);
		}

		// Setup the parameters for the enabled alert
		Map<String, Object> customParams = new HashMap<>();
		customParams.put(DISABLED, false);
		customParams.put(NOTIFICATION_ENABLED, true);

		// Create an alert enablement checker
		AlertConfigChecker enablementChecker = new AlertConfigChecker() {
			@Override
			public boolean isAlertProperlyConfigured(JsonNode alert, JsonNode alerts) {
				// Check if alert is enabled
				return !alert.path(DISABLED).asBoolean(false) && alert.path(NOTIFICATION_ENABLED).asBoolean(false);
			}
		};

		// Use the common method to process the alerts
		return processAlerts(
				alertsDefinition,
				requiredAlerts,
				allAlerts,
				customParams,
				enablementChecker,
				"Successfully created and enabled all required alerts",
				"Failed to create some alerts: ",
				"All required alerts are already duplicated and enabled"
		);
	}

	/**
	 * Creates new alerts from YAML definitions with default "TenantAdmins" notification.
	 * This is used for alerts that were not found in the existing alerts but are required.
	 *
	 * @return CompletableFuture with the result
	 */
	protected CompletableFuture<JsonNode> createNewAlerts(Map<String, AlertDefinition> alertDefinition) {
		if (newAlerts.isEmpty()) {
			logger.info("No new alerts to create");
			return CompletableFuture.completedFuture(createSuccessNode("No new alerts to create"));
		}

		logger.info("Creating {} new enabled alerts: {}", newAlerts.size(), newAlerts);

		// Use the default TenantAdmins recipient
		List<String> defaultRecipients = Collections.singletonList(DEFAULT_NOTIFY_USER);

		return createAlertsFromDefinitions(alertDefinition, newAlerts, defaultRecipients);
	}

	/**
	 * Checks if the alert response is valid and contains alerts.
	 */
	protected boolean isValidAlertResponse(JsonNode allAlerts) {
		return allAlerts != null && allAlerts.isArray() && allAlerts.size() > 0;
	}

	/**
	 * Identifies required alerts from all alerts.
	 *
	 * @param allAlerts          The JSON response containing all alerts
	 * @param existingAlertNames List to populate with all alert names (for duplicate checking)
	 * @return List of required alerts that match our criteria
	 */
	protected List<JsonNode> identifyRequiredAlerts(JsonNode allAlerts, List<String> existingAlertNames) {
		List<JsonNode> requiredAlerts = new ArrayList<>();

		// Collect all alert names first to check for duplicates
		if (existingAlertNames != null) {
			for (JsonNode alert : allAlerts) {
				String alertName = alert.path(Constants.NAME_FIELD).asText();
				existingAlertNames.add(alertName);
			}
		}

		// Find the required alerts
		for (JsonNode alert : allAlerts) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			// Check if this is one of our required alerts
			for (String requiredAlertName : alertsToConfigure) {
				if (alertName.contains(requiredAlertName)) {
					// Found a required alert
					requiredAlerts.add(alert);
					this.newAlerts.remove(requiredAlertName);
					break;
				}
			}
		}

		return requiredAlerts;
	}

	/**
	 * Interface for checking if an alert is properly configured.
	 */
	protected interface AlertConfigChecker {
		/**
		 * Check if an alert is properly configured according to specific requirements.
		 *
		 * @param alert     The alert to check
		 * @param allAlerts All alerts (may be null if not needed)
		 * @return true if the alert is properly configured
		 */
		boolean isAlertProperlyConfigured(JsonNode alert, JsonNode allAlerts);
	}

	/**
	 * Create an error node
	 */
	protected JsonNode createErrorNode(String error) {
		ObjectNode errorNode = jsonMapper.createObjectNode();
		errorNode.put(Constants.ERROR_FIELD, error);
		return errorNode;
	}

	/**
	 * Create a success response node.
	 */
	protected JsonNode createSuccessNode(String message) {
		ObjectNode node = jsonMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.SUCCESS_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Create a failure response node.
	 */
	protected JsonNode createFailureNode(String message) {
		ObjectNode node = jsonMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, Constants.FAILURE_STATUS);
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Create a partial success response node.
	 */
	protected JsonNode createPartialSuccessNode(String message) {
		ObjectNode node = jsonMapper.createObjectNode();
		node.put(Constants.STATUS_FIELD, "partial");
		node.put(Constants.POLICY_ID_FIELD, getPolicyId());
		node.put(Constants.MESSAGE_FIELD, message);
		return node;
	}

	/**
	 * Creates new alerts from YAML alert definitions.
	 */
	protected CompletableFuture<JsonNode> createAlertsFromDefinitions(
			Map<String, AlertDefinition> alertsMap,
			Set<String> alertsToCreate,
			List<String> notifyUsers) {

		if (alertsToCreate.isEmpty()) {
			logger.info("No new alerts to create");
			return CompletableFuture.completedFuture(
					createSuccessNode("No new alerts to create")
			);
		}

		logger.info("Creating {} new alerts from definitions: {}",
				alertsToCreate.size(), alertsToCreate);

		try {
			List<CompletableFuture<JsonNode>> creationTasks = new ArrayList<>();
			List<String> createdAlerts = new ArrayList<>();
			List<String> failedAlerts = new ArrayList<>();
			List<String> skippedAlerts = new ArrayList<>();

			// Use default recipient if notifyUsers is empty
			List<String> recipients = notifyUsers;
			if (CollectionUtils.isEmpty(recipients)) {
				recipients = Collections.singletonList(DEFAULT_NOTIFY_USER);
				logger.info("Using default notification recipient: {}", DEFAULT_NOTIFY_USER);
			}

			String notifyUsersString = String.join(",", recipients);

			// Process each alert to create
			for (String alertName : alertsToCreate) {
				AlertDefinition alertDef = findAlertDefinitionByName(alertsMap, alertName);

				if (alertDef == null) {
					logger.warn("No definition found for alert '{}'", alertName);
					skippedAlerts.add(alertName);
					continue;
				}

				Map<String, Object> parameters = new HashMap<>(alertDef.getParametersMap());
				parameters.put("Name", alertDef.getName() + tenantHashSuffix);
				parameters.put("NotifyUser", notifyUsersString);
				parameters.put("NotificationEnabled", true);
				parameters.put("Disabled", false);

				creationTasks.add(exchangeClient.executeCmdletCommand(
								new CommandRequest(NEW_PROTECTION_ALERT, parameters, this.csspEndpoint))
						.thenApply(result -> {
							String displayName = alertDef.getName();
							if (result != null && !result.has(Constants.ERROR_FIELD)) {
								logger.info("Successfully created alert: {}", displayName);
								createdAlerts.add(displayName);
							} else {
								String errorMessage = result != null ?
										extractErrorMessage(result) : "Unknown error";
								logger.error("Failed to create alert '{}': {}", displayName, errorMessage);
								failedAlerts.add(displayName);
							}
							return result;
						})
				);
			}

			if (creationTasks.isEmpty()) {
				if (!skippedAlerts.isEmpty()) {
					return CompletableFuture.completedFuture(
							createPartialSuccessNode("Could not find definitions for some alerts: " +
													 String.join(", ", skippedAlerts))
					);
				} else {
					return CompletableFuture.completedFuture(
							createSuccessNode("No new alerts to create")
					);
				}
			}

			return CompletableFuture.allOf(creationTasks.toArray(new CompletableFuture[0]))
					.thenApply(v -> {
						if (failedAlerts.isEmpty()) {
							if (skippedAlerts.isEmpty()) {
								return createSuccessNode("Successfully created all new alerts");
							} else {
								return createPartialSuccessNode("Created some alerts but could not find definitions for: " +
																String.join(", ", skippedAlerts));
							}
						} else {
							StringBuilder errorMessage = new StringBuilder("Failed to create some alerts: ");
							for (String failed : failedAlerts) {
								errorMessage.append(failed).append(", ");
							}
							if (!createdAlerts.isEmpty() || !skippedAlerts.isEmpty()) {
								return createPartialSuccessNode(errorMessage.toString());
							} else {
								return createFailureNode(errorMessage.toString());
							}
						}
					});

		} catch (Exception ex) {
			logger.error("Error creating alerts from definitions", ex);
			return CompletableFuture.completedFuture(
					createFailureNode("Failed to create alerts from definitions: " + ex.getMessage())
			);
		}
	}

	/**
	 * Finds an alert definition by matching the name or ID.
	 */
	private AlertDefinition findAlertDefinitionByName(Map<String, AlertDefinition> alertsMap, String alertName) {
		if (alertsMap.containsKey(alertName)) {
			return alertsMap.get(alertName);
		}

		String lowerAlertName = alertName.toLowerCase();

		for (AlertDefinition def : alertsMap.values()) {
			String defName = def.getName().toLowerCase();

			if (defName.equals(lowerAlertName)) {
				return def;
			}

			if (defName.contains(lowerAlertName) || lowerAlertName.contains(defName)) {
				return def;
			}
		}

		return null;
	}

	/**
	 * Extract error message from the result.
	 */
	protected String extractErrorMessage(JsonNode result) {
		if (result == null) {
			return "No response received";
		}

		if (result.has(Constants.ERROR_FIELD)) {
			JsonNode error = result.get(Constants.ERROR_FIELD);
			if (error.isTextual()) {
				return error.asText();
			} else if (error.isObject()) {
				if (error.has(Constants.MESSAGE_FIELD)) {
					return error.get(Constants.MESSAGE_FIELD).asText();
				} else if (error.has(Constants.DETAILS_FIELD)
						   && error.get(Constants.DETAILS_FIELD).isArray()
						   && error.get(Constants.DETAILS_FIELD).size() > 0) {
					JsonNode details = error.get(Constants.DETAILS_FIELD).get(0);
					if (details.has(Constants.MESSAGE_FIELD)) {
						return details.get(Constants.MESSAGE_FIELD).asText();
					}
				}

				if (error.has("innererror")) {
					JsonNode innerError = error.get("innererror");
					if (innerError.has(Constants.MESSAGE_FIELD)) {
						return innerError.get(Constants.MESSAGE_FIELD).asText();
					}

					if (innerError.has("internalexception")) {
						JsonNode internalException = innerError.get("internalexception");
						if (internalException.has(Constants.MESSAGE_FIELD)) {
							return internalException.get(Constants.MESSAGE_FIELD).asText();
						}
					}
				}
			}
		}

		return "Unknown error: " + (result.toString().length() > 100 ?
				result.toString().substring(0, 100) + "..." : result.toString());
	}

	/**
	 * Processes a list of alerts by either duplicating them or ensuring they have the correct configuration.
	 */
	protected CompletableFuture<JsonNode> processAlerts(
			Map<String, AlertDefinition> alertsDefinition,
			List<JsonNode> alertsToProcess,
			JsonNode allAlerts,
			Map<String, Object> customParameters,
			AlertConfigChecker configChecker,
			String successMessage,
			String partialSuccessPrefix,
			String skipMessage) {

		List<CompletableFuture<JsonNode>> processingTasks = new ArrayList<>();
		List<String> processedAlerts = new ArrayList<>();
		List<String> failedAlerts = new ArrayList<>();
		List<String> skippedAlerts = new ArrayList<>();

		// First, deduplicate alerts by their name to ensure we don't process the same alert twice
		Map<String, JsonNode> uniqueAlerts = new HashMap<>();
		for (JsonNode alert : alertsToProcess) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();
			if (!uniqueAlerts.containsKey(alertName)) {
				uniqueAlerts.put(alertName, alert);
			}
		}

		logger.info("Processing {} unique alerts after deduplication", uniqueAlerts.size());

		for (JsonNode alert : uniqueAlerts.values()) {
			String alertName = alert.path(Constants.NAME_FIELD).asText();

			// Check if alert is already properly configured (if a checker is provided)
			if (configChecker != null && configChecker.isAlertProperlyConfigured(alert, allAlerts)) {
				logger.info("Alert '{}' is already enabled with correct configuration", alertName);
				skippedAlerts.add(alertName);
				processedAlerts.add(alertName);
				continue;
			}

			// Check if there's an existing custom version with the tenant suffix
			JsonNode existingCustomAlert = findExistingCustomAlert(allAlerts, alert, tenantHashSuffix);
			if (existingCustomAlert != null) {
				String existingAlertName = existingCustomAlert.path(Constants.NAME_FIELD).asText();

				// Check if existing custom alert already has correct settings
				if (configChecker != null && configChecker.isAlertProperlyConfigured(existingCustomAlert, null)) {
					logger.info("Existing custom alert '{}' already has correct configuration", existingAlertName);
					processedAlerts.add(alertName);
					continue;
				}

				// TODO: If needed, update existing alert settings
				// For now, we'll log it and consider it processed
				logger.info("Found existing custom alert '{}' but configuration needs updating", existingAlertName);
				processedAlerts.add(alertName);
				continue;
			}

			Map<String, Object> customParametersTmp = new HashMap<>(customParameters);
			AlertDefinition tmpAlertDefinition = alertsDefinition.get(alertName);
			if (tmpAlertDefinition != null) {
				customParametersTmp.putAll(tmpAlertDefinition.getParametersMap());
			}

			// No existing properly configured alert, create a new one
			processingTasks.add(duplicateOrUpdateAlert(alert, allAlerts, tenantHashSuffix, customParametersTmp)
					.thenApply(result -> {
						if (result != null && !result.has(Constants.ERROR_FIELD)) {
							processedAlerts.add(alertName);
						} else {
							failedAlerts.add(alertName);
						}
						return result;
					}));
		}

		if (processingTasks.isEmpty()) {
			if (!skippedAlerts.isEmpty()) {
				logger.info("All required alerts are already correctly configured or have custom versions");
				return CompletableFuture.completedFuture(
						createSuccessNode(skipMessage)
				);
			} else {
				logger.info("All required alerts already have custom versions with the correct configuration");
				return CompletableFuture.completedFuture(
						createSuccessNode("All required alerts already have custom versions with the correct configuration")
				);
			}
		}

		return CompletableFuture.allOf(processingTasks.toArray(new CompletableFuture[0]))
				.thenApply(v -> {
					if (failedAlerts.isEmpty()) {
						if (skippedAlerts.size() == processedAlerts.size()) {
							// All alerts were skipped because they were already properly configured
							return createSuccessNode(skipMessage);
						} else if (!skippedAlerts.isEmpty()) {
							// Some alerts were skipped, some were created
							return createSuccessNode(successMessage + " Some alerts were already correctly configured.");
						} else {
							// All alerts were newly configured
							return createSuccessNode(successMessage);
						}
					} else {
						StringBuilder errorMessage = new StringBuilder(partialSuccessPrefix);
						for (String failed : failedAlerts) {
							errorMessage.append(failed).append(", ");
						}
						// If we've processed some alerts successfully but not all
						if (!processedAlerts.isEmpty()) {
							return createPartialSuccessNode(errorMessage.toString());
						} else {
							return createFailureNode(errorMessage.toString());
						}
					}
				});
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	private static class AlertConfigItem {
		public String id;
		public String name;
		public String description; //not saved to config
		public String category;
		public String severity;
		public List<String> operation;
		public Boolean notificationEnabled;
		public String aggregationType;
		public String threatType;
		public String threshold;
		public String filter;
		public Boolean disabled;
	}
}